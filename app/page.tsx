"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { useEffect, useRef, useState } from "react"
import UseCases from "@/components/use-cases"

export default function HomePage() {
  const mysteryLayerRef = useRef<HTMLDivElement>(null)
  const spotRef = useRef<HTMLDivElement>(null)
  const heroNumberRef = useRef<HTMLHeadingElement>(null)
  const heroTitleRef = useRef<HTMLParagraphElement>(null)
  const heroTaglineRef = useRef<HTMLParagraphElement>(null)
  const heroCtaRef = useRef<HTMLDivElement>(null)
  const heroButtonRef = useRef<HTMLButtonElement>(null)
  const [typedLines, setTypedLines] = useState<string[]>(["", "", "", "", "", "", ""])
  const [activeLine, setActiveLine] = useState<number>(0)

  const terminalLines = [
    "Analyzing deep packets...",
    "Locating problem position...",
    "Providing actionable evidence...",
    "Explaining possible causes...",
    "Suggesting optimal solutions...",
    "Ready to implement fix",
    "Estimated resolution time: 3 minutes",
  ]

  useEffect(() => {
    const host = mysteryLayerRef.current
    const spot = spotRef.current
    if (!host || !spot) return

    let hostRect = host.getBoundingClientRect()
    let mouseX = 0
    let mouseY = 0
    let rafId = 0
    let needsUpdate = false
    let hovered = false
    const radius = 120 // spotlight radius (px) for 240px diameter
    let isInitialized = false

    const problems = Array.from(host.querySelectorAll<HTMLElement>(".network-problem"))
    let centers: Array<{ el: HTMLElement; cx: number; cy: number }> = []

    const computeCenters = () => {
      centers = problems.map((el) => {
        const r = el.getBoundingClientRect()
        return { el, cx: r.left + r.width / 2, cy: r.top + r.height / 2 }
      })
    }

    const rectsIntersect = (
      a: { left: number; top: number; right: number; bottom: number },
      b: { left: number; top: number; right: number; bottom: number },
    ) => {
      return !(a.right < b.left || a.left > b.right || a.bottom < b.top || a.top > b.bottom)
    }

    const placeProblems = () => {
      const padding = 24
      const gap = 20 // minimal spacing between labels
      const reservedViewport: Array<{ left: number; top: number; right: number; bottom: number }> = []

      const addRect = (r: DOMRect | null) => {
        if (!r) return
        reservedViewport.push({ left: r.left, top: r.top, right: r.right, bottom: r.bottom })
      }

      addRect(heroNumberRef.current?.getBoundingClientRect() ?? null)
      addRect(heroTitleRef.current?.getBoundingClientRect() ?? null)
      addRect(heroTaglineRef.current?.getBoundingClientRect() ?? null)
      addRect(heroCtaRef.current?.getBoundingClientRect() ?? null)

      const unionReserved = reservedViewport.reduce(
        (acc, r) => ({
          left: Math.min(acc.left, r.left),
          top: Math.min(acc.top, r.top),
          right: Math.max(acc.right, r.right),
          bottom: Math.max(acc.bottom, r.bottom),
        }),
        {
          left: Number.POSITIVE_INFINITY,
          top: Number.POSITIVE_INFINITY,
          right: Number.NEGATIVE_INFINITY,
          bottom: Number.NEGATIVE_INFINITY,
        },
      )
      // Convert to local union rect
      const unionLocal =
        unionReserved.left === Number.POSITIVE_INFINITY
          ? null
          : {
              left: Math.max(0, unionReserved.left - hostRect.left),
              top: Math.max(0, unionReserved.top - hostRect.top),
              right: Math.min(hostRect.width, unionReserved.right - hostRect.left),
              bottom: Math.min(hostRect.height, unionReserved.bottom - hostRect.top),
            }

      // Also keep individual red-frame rectangles (number/title/tagline/button) in local coords
      const reservedLocalList = reservedViewport
        .map((r) => ({
          left: Math.max(0, r.left - hostRect.left),
          top: Math.max(0, r.top - hostRect.top),
          right: Math.min(hostRect.width, r.right - hostRect.left),
          bottom: Math.min(hostRect.height, r.bottom - hostRect.top),
        }))
        .filter((r) => r.right > r.left && r.bottom > r.top)

      // Occupancy grid (poisson-like) placement, guarantees no overlap
      type NodeBox = { el: HTMLElement; w: number; h: number; x: number; y: number }
      const nodes: NodeBox[] = problems.map((el) => ({
        el,
        w: el.offsetWidth || 200,
        h: el.offsetHeight || 40,
        x: 0,
        y: 0,
      })) as NodeBox[]

      const cols = Math.max(6, Math.round(hostRect.width / 220))
      const rows = Math.max(4, Math.round(hostRect.height / 120))
      const cellW = (hostRect.width - padding * 2) / cols
      const cellH = (hostRect.height - padding * 2) / rows

      const occupied: boolean[][] = Array.from({ length: rows }, () => Array<boolean>(cols).fill(false))

      const candidates: Array<{ r: number; c: number }> = []
      for (let r = 0; r < rows; r++) for (let c = 0; c < cols; c++) candidates.push({ r, c })
      for (let i = candidates.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1))
        ;[candidates[i], candidates[j]] = [candidates[j], candidates[i]]
      }

      const tryPlace = (n: NodeBox): boolean => {
        const spanC = Math.min(cols, Math.max(1, Math.ceil((n.w + gap) / cellW)))
        const spanR = Math.min(rows, Math.max(1, Math.ceil((n.h + gap) / cellH)))
        const order = candidates.slice()
        for (let i = order.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1))
          ;[order[i], order[j]] = [order[j], order[i]]
        }
        for (const { r, c } of order) {
          if (r + spanR > rows || c + spanC > cols) continue
          let ok = true
          for (let rr = r; rr < r + spanR && ok; rr++)
            for (let cc = c; cc < c + spanC; cc++)
              if (occupied[rr][cc]) {
                ok = false
                break
              }
          if (!ok) continue
          // place with jitter inside spanned cells
          const x0 = padding + c * cellW
          const y0 = padding + r * cellH
          const widthSpan = spanC * cellW
          const heightSpan = spanR * cellH
          const maxX = Math.max(0, widthSpan - n.w)
          const maxY = Math.max(0, heightSpan - n.h)
          n.x = x0 + (maxX > 0 ? Math.random() * maxX : 0)
          n.y = y0 + (maxY > 0 ? Math.random() * maxY : 0)

          // reject if candidate region intersects any reserved red-frame rect (with margin)
          const margin = 12
          const labelRect = { left: n.x, top: n.y, right: n.x + n.w, bottom: n.y + n.h }
          const intersectsReserved = reservedLocalList.some((rr) => {
            const expanded = {
              left: Math.max(0, rr.left - margin),
              top: Math.max(0, rr.top - margin),
              right: Math.min(hostRect.width, rr.right + margin),
              bottom: Math.min(hostRect.height, rr.bottom + margin),
            }
            return !(
              labelRect.right < expanded.left ||
              labelRect.left > expanded.right ||
              labelRect.bottom < expanded.top ||
              labelRect.top > expanded.bottom
            )
          })
          if (intersectsReserved) continue

          for (let rr = r; rr < r + spanR; rr++) for (let cc = c; cc < c + spanC; cc++) occupied[rr][cc] = true
          return true
        }
        return false
      }

      for (const n of nodes) {
        if (!tryPlace(n)) {
          // fallback: reduce span requirements slightly by allowing overlap margin reduction
          n.w = Math.min(n.w, cellW * 2)
          n.h = Math.min(n.h, cellH * 2)
          tryPlace(n)
        }
      }

      // write back positions (no shadows by default)
      const margin = 16
      for (const n of nodes) {
        n.el.style.left = `${Math.round(n.x)}px`
        n.el.style.top = `${Math.round(n.y)}px`
        n.el.style.textShadow = "none"
      }
    }

    const updateRectAndCenters = () => {
      hostRect = host.getBoundingClientRect()
      placeProblems()
      computeCenters()
      isInitialized = true
    }

    // Initialize with a small delay to ensure layout is settled
    const initializeSpotlight = () => {
      // Use requestAnimationFrame to ensure DOM is fully rendered
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          updateRectAndCenters()
        })
      })
    }

    // Initialize immediately and also after a short delay as fallback
    initializeSpotlight()
    const initTimeout = setTimeout(initializeSpotlight, 100)

    // Spotlight falloff configuration
    const innerRadius = 60 // fully bright core radius
    const outerRadius = 140 // edge of spotlight influence where labels fade out

    const update = () => {
      needsUpdate = false
      if (!hovered) {
        spot.style.transform = "translate3d(-9999px,-9999px,0)"
        for (const { el } of centers) el.style.opacity = "0.01"
        return
      }

      const x = mouseX - hostRect.left - radius
      const y = mouseY - hostRect.top - radius
      spot.style.transform = `translate3d(${x}px, ${y}px, 0)`

      for (const { el, cx, cy } of centers) {
        const dx = mouseX - cx
        const dy = mouseY - cy
        const d = Math.hypot(dx, dy)
        // Smooth spotlight falloff using smoothstep
        let u: number
        if (d <= innerRadius) {
          u = 1
        } else if (d >= outerRadius) {
          u = 0
        } else {
          const t = (d - innerRadius) / (outerRadius - innerRadius)
          // smoothstep inverted: 1 - smoothstep(0,1,t)
          u = 1 - t * t * (3 - 2 * t)
        }
        const minOpacity = 0.02
        const maxOpacity = 0.9
        const opacity = minOpacity + (maxOpacity - minOpacity) * u
        el.style.opacity = String(opacity)
      }
    }

    const onMove = (e: MouseEvent | PointerEvent) => {
      // Ensure initialization has completed before processing mouse events
      if (!isInitialized) {
        initializeSpotlight()
        return
      }

      // both MouseEvent and PointerEvent have clientX/Y
      const x = (e as MouseEvent).clientX
      const y = (e as MouseEvent).clientY
      mouseX = x
      mouseY = y

      const wasHovered = hovered
      hovered = x >= hostRect.left && x <= hostRect.right && y >= hostRect.top && y <= hostRect.bottom

      if (hovered && !wasHovered) {
        computeCenters()
      }

      if (!needsUpdate) {
        needsUpdate = true
        rafId = requestAnimationFrame(update)
      }
    }

    const onResize = () => updateRectAndCenters()
    const onScroll = () => updateRectAndCenters()

    // Add ResizeObserver for more reliable layout detection
    let resizeObserver: ResizeObserver | null = null
    if (typeof ResizeObserver !== 'undefined') {
      resizeObserver = new ResizeObserver(() => {
        updateRectAndCenters()
      })
      resizeObserver.observe(host)
    }

    window.addEventListener("pointermove", onMove as any, { passive: true })
    window.addEventListener("mousemove", onMove as any, { passive: true })
    window.addEventListener("resize", onResize, { passive: true } as AddEventListenerOptions)
    window.addEventListener("scroll", onScroll, { passive: true } as AddEventListenerOptions)

    return () => {
      clearTimeout(initTimeout)
      if (resizeObserver) {
        resizeObserver.disconnect()
      }
      window.removeEventListener("pointermove", onMove as any)
      window.removeEventListener("mousemove", onMove as any)
      window.removeEventListener("resize", onResize as any)
      window.removeEventListener("scroll", onScroll as any)
      cancelAnimationFrame(rafId)
    }
  }, [])

  // Terminal-like typing effect for right column lines with looping and end pause + caret blink
  useEffect(() => {
    let cancelled = false
    const delay = (ms: number) => new Promise((r) => setTimeout(r, ms))
    ;(async () => {
      while (!cancelled) {
        // reset for a new cycle
        setTypedLines(["", "", "", "", "", "", ""])
        for (let i = 0; i < terminalLines.length; i++) {
          if (cancelled) return
          setActiveLine(i)
          const text = terminalLines[i]
          for (let j = 0; j <= text.length; j++) {
            if (cancelled) return
            setTypedLines((prev) => {
              const next = prev.slice()
              next[i] = text.slice(0, j)
              return next
            })
            await delay(22)
          }
          await delay(420)
        }
        // After finishing the last line, keep caret blinking on the last line for a few seconds
        setActiveLine(terminalLines.length - 1)
        await delay(3000)
        setActiveLine(-1)
      }
    })()

    return () => {
      cancelled = true
    }
  }, [])

  // Removed leading loader animation per new design; keep caret-only typing

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-8 h-16 bg-white/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="flex items-center space-x-3">
          <img src="/42logo-figma.svg" alt="42 logo" className="w-[42px] h-[42px]" />
        </div>
        <div className="hidden md:flex items-center space-x-10">
          <a href="#" className="text-slate-600 hover:text-cyan-800 transition-colors font-medium">
            Home
          </a>
          <a href="#" className="text-slate-600 hover:text-cyan-800 transition-colors font-medium">
            Features
          </a>
          <a href="#" className="text-slate-600 hover:text-cyan-800 transition-colors font-medium">
            Solutions
          </a>
          <a href="#" className="text-slate-600 hover:text-cyan-800 transition-colors font-medium">
            Pricing
          </a>
          <a href="#" className="text-slate-600 hover:text-cyan-800 transition-colors font-medium">
            Docs
          </a>
        </div>
        <div className="flex items-center space-x-4">
          <select className="bg-transparent text-sm text-slate-600 font-medium">
            <option>English</option>
          </select>
          <Button className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-400 hover:to-emerald-500 text-white px-6 py-2 font-semibold transition-all duration-300 rounded-md cursor-pointer">
            Get Started
          </Button>
        </div>
      </nav>

      {/* Hero Section - Lovart-inspired dark artistic background */}
      <section className="relative bg-gradient-to-br from-slate-900 via-cyan-900 to-slate-800 text-white overflow-hidden min-h-screen flex items-center">
        {/* Artistic background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl animate-float"></div>
          <div
            className="absolute bottom-20 right-20 w-80 h-80 bg-emerald-500/10 rounded-full blur-3xl animate-float"
            style={{ animationDelay: "2s" }}
          ></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-cyan-500/5 to-emerald-500/5 rounded-full blur-3xl"></div>
        </div>

        {/* Network visualization overlay */}
        <div className="absolute inset-0 opacity-5">
          <svg className="w-full h-full" viewBox="0 0 1200 800">
            <defs>
              <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
            <circle cx="200" cy="150" r="4" fill="currentColor" className="animate-pulse" />
            <circle
              cx="400"
              cy="300"
              r="4"
              fill="currentColor"
              className="animate-pulse"
              style={{ animationDelay: "1s" }}
            />
            <circle
              cx="800"
              cy="200"
              r="4"
              fill="currentColor"
              className="animate-pulse"
              style={{ animationDelay: "2s" }}
            />
            <circle
              cx="1000"
              cy="400"
              r="4"
              fill="currentColor"
              className="animate-pulse"
              style={{ animationDelay: "0.5s" }}
            />
            <line x1="200" y1="150" x2="400" y2="300" stroke="currentColor" strokeWidth="1" opacity="0.3" />
            <line x1="400" y1="300" x2="800" y2="200" stroke="currentColor" strokeWidth="1" opacity="0.3" />
            <line x1="800" y1="200" x2="1000" y2="400" stroke="currentColor" strokeWidth="1" opacity="0.3" />
          </svg>
        </div>

        <div ref={mysteryLayerRef} className="absolute inset-0 z-0 cursor-none mystery-layer">
          <div ref={spotRef} className="spotlight" />
          <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            PACKET LOSS
          </div>
          <div className="network-problem absolute text-white font-serif text-3xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            DNS TIMEOUT
          </div>
          <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            BANDWIDTH THROTTLING
          </div>
          <div className="network-problem absolute text-white font-serif text-3xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            CONNECTION REFUSED
          </div>
          <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            LATENCY SPIKE
          </div>
          <div className="network-problem absolute text-white font-serif text-3xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            SSL HANDSHAKE FAILED
          </div>
          <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            ARP POISONING
          </div>
          <div className="network-problem absolute text-white font-serif text-3xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            DHCP EXHAUSTION
          </div>
          <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            ROUTING LOOP
          </div>
          <div className="network-problem absolute text-white font-serif text-3xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            FIREWALL BLOCKED
          </div>
          <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            PORT UNREACHABLE
          </div>
          <div className="network-problem absolute text-white font-serif text-2xl font-bold transition-opacity duration-200 will-change-opacity pointer-events-none">
            VLAN MISMATCH
          </div>
        </div>

        <div className="relative z-20 container mx-auto px-8 py-20" ref={heroCtaRef}>
          <div className="max-w-5xl mx-auto text-center">
            <h1
              ref={heroNumberRef}
              className="font-serif font-bold text-8xl md:text-[12rem] mb-8 tracking-tight"
              style={{ textShadow: "0 4px 12px rgba(0,0,0,0.15)" }}
            >
              <span className="bg-gradient-to-r from-white via-cyan-50 to-emerald-50 bg-clip-text text-transparent">
                42
              </span>
            </h1>

            <p
              ref={heroTitleRef}
              className="text-3xl md:text-5xl text-emerald-400 font-medium mb-10 tracking-wide font-space-grotesk"
              style={{ textShadow: "0 2px 8px rgba(0,0,0,0.12)" }}
            >
              Don't troubleshoot. Just know.
            </p>

            <p
              ref={heroTaglineRef}
              className="text-2xl md:text-3xl text-cyan-100/90 max-w-4xl mx-auto mb-16 leading-relaxed font-serif"
              style={{ textShadow: "0 2px 6px rgba(0,0,0,0.1)" }}
            >
              From raw packets to real answers.
            </p>

            <Button
              size="lg"
              className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-400 hover:to-emerald-500 text-white px-16 py-8 text-2xl font-semibold rounded-md transition-all duration-300 transform hover:scale-105 cursor-pointer"
              style={{ boxShadow: "0 4px 16px rgba(0,0,0,0.15)" }}
            >
              Find Your Answer In Minutes
            </Button>
          </div>
        </div>
      </section>

      {/* Tagline Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-8 text-center">
          <h2 className="font-serif font-bold text-5xl md:text-7xl text-cyan-800 mb-8 tracking-tight">
            AI Understands Your Network
          </h2>
          <p className="text-2xl text-slate-600 max-w-5xl mx-auto leading-relaxed font-light">
            Every minute of downtime costs more than money. <br />
            42 helps you fix issues before they impact your business.
          </p>
        </div>
      </section>

      {/* Why 42 Section */}
      <section className="bg-white py-20">
        <div className="container mx-auto px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-20 items-start content-stretch">
              <div>
                <h2 className="font-serif font-bold text-5xl text-cyan-800 mb-8 tracking-tight">
                  Just give the packets.
                </h2>
                <p className="text-xl text-slate-600 leading-relaxed mb-6 font-light">
                  42 plans, explores, and creates like a real troubleshooting expert — calling the right tools, mapping
                  out solutions, and bringing your technical vision to life.
                </p>

                <div className="bg-gradient-to-br from-cyan-50 to-emerald-50 px-6 pt-4 pb-6 rounded-2xl italic">
                  <div className="flex items-center space-x-4 mb-4 pl-[1ch]">
                    <span className="text-slate-500 font-medium">Describe Your Network Issue</span>
                  </div>
                  <p className="text-slate-800 italic font-medium text-lg leading-snug mb-6 pl-[1ch]">
                    Intermittent connectivity drops during peak hours.
                  </p>
                  <div className="flex items-center justify-start pl-[1ch]">
                    <p className="text-slate-800 font-bold text-lg mt-1 py-1">Diagnose Now {">>>"}</p>
                  </div>
                </div>
              </div>

              <div className="relative">
                <h2 className="font-serif font-bold text-5xl text-cyan-800 mb-8 tracking-tight text-left">
                  Let 42 handle the rest.
                </h2>
                <div className="overflow-hidden">
                  {/* Rounded rectangle, auto height, gradient background consistent with left */}
                  <div className="relative rounded-2xl px-6 py-6 bg-gradient-to-br from-cyan-50 to-emerald-50">
                    <div className="space-y-3">
                      {terminalLines.map((line, idx) => (
                        <div key={idx} className="text-slate-700 font-mono pl-[1ch]">
                          {idx === 5 ? (
                            <span className="font-bold">{typedLines[idx]}</span>
                          ) : (
                            <span>{typedLines[idx]}</span>
                          )}
                          {idx === 5 && typedLines[5] === line ? <span className="ml-2 font-bold">{">>>"}</span> : null}
                          {activeLine === idx ? <span className="typing-caret" /> : null}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <UseCases />

      {/* Video Demonstration Section */}
      <section className="bg-white py-32">
        <div className="container mx-auto px-8">
          <div className="max-w-7xl mx-auto">
            <div className="relative max-w-6xl mx-auto">
              <div className="relative bg-white overflow-hidden">
                <div className="aspect-video">
                  <iframe
                    src="https://www.youtube.com/embed/SgmuplXU2iY"
                    title="42 Network Troubleshooting Demo"
                    className="w-full h-full"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  ></iframe>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20" style={{ backgroundColor: "rgb(238, 238, 238)" }}>
        <div className="container mx-auto px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-20">
              <h2 className="font-serif font-bold text-5xl text-cyan-800 mb-6 tracking-tight">Smart Diagnostics</h2>
              <p className="text-xl text-slate-600 max-w-3xl mx-auto font-light">
                Experience the power of AI-driven troubleshooting with comprehensive analysis and intelligent solutions
              </p>
            </div>

            <div className="grid lg:grid-cols-3 gap-10">
              <Card className="p-10 bg-gradient-to-br from-white to-cyan-50/50 hover:bg-gradient-to-br hover:from-cyan-50/30 hover:to-cyan-100/50 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white text-2xl">🔍</span>
                </div>
                <h3 className="font-serif font-bold text-2xl text-cyan-800 mb-6">Diagnostic Power</h3>
                <div className="space-y-4 text-slate-600">
                  <p className="font-medium">Autonomous packet parsing and deep analysis</p>
                  <p className="font-medium">Instant problem detection and root-cause pinpointing</p>
                  <p className="font-medium">Evidence collection with clear, reliable reports</p>
                </div>
              </Card>

              <Card className="p-10 bg-gradient-to-br from-white to-emerald-50/50 hover:bg-gradient-to-br hover:from-emerald-50/30 hover:to-emerald-100/50 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-br from-cyan-600 to-cyan-700 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white text-2xl">🧠</span>
                </div>
                <h3 className="font-serif font-bold text-2xl text-cyan-800 mb-6">Knowledge at Your Fingertips</h3>
                <div className="space-y-4 text-slate-600">
                  <p className="font-medium">Comprehensive expertise in TCP/IP, networking, and system operations</p>
                  <p className="font-medium">Context-aware intelligence that understands complex environments</p>
                  <p className="font-medium">Continuously evolving knowledge base that learns from every case</p>
                </div>
              </Card>

              <Card className="p-10 bg-gradient-to-br from-white to-slate-50 hover:bg-gradient-to-br hover:from-slate-50/50 hover:to-slate-100/50 transition-all duration-300 group">
                <div className="w-16 h-16 bg-gradient-to-br from-slate-600 to-slate-700 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white text-2xl">⚡</span>
                </div>
                <h3 className="font-serif font-bold text-2xl text-cyan-800 mb-6">Solutions that Work</h3>
                <div className="space-y-4 text-slate-600">
                  <p className="font-medium">Step-by-step verification methods you can trust</p>
                  <p className="font-medium">Targeted, effective fixes tailored to each unique issue</p>
                  <p className="font-medium">Preventive recommendations to stop problems before they happen</p>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-br from-slate-900 via-cyan-900 to-slate-800 text-white relative overflow-hidden py-32">
        <div className="container mx-auto px-8 text-center relative z-10">
          <div className="max-w-5xl mx-auto">
            <h2 className="font-serif font-bold text-5xl md:text-7xl mb-8 tracking-tight">From confusion to clarity</h2>

            <p className="text-3xl text-emerald-400 mb-12 font-serif font-medium">
              Cut troubleshooting time. Keep your revenue flowing.
            </p>
            <Button
              size="lg"
              className="bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-400 hover:to-emerald-500 text-white px-12 py-6 text-xl font-semibold rounded-md transition-all duration-300 transform hover:scale-105 cursor-pointer"
            >
              Start Diagnosing Now
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white py-16">
        <div className="container mx-auto px-8">
          <div className="text-center">
            <div className="text-slate-400 font-medium">© 2025 42, Inc. All rights reserved.</div>
          </div>
        </div>
      </footer>
    </div>
  )
}
