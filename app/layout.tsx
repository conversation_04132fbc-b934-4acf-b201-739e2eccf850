import type React from "react"
import type { Metada<PERSON> } from "next"
import { Space_Grotesk, DM_Sans, Roboto_Mono } from "next/font/google"
import "./globals.css"

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-space-grotesk",
})

const dmSans = DM_Sans({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-dm-sans",
})

const robotoMono = Roboto_Mono({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-roboto-mono",
})

export const metadata: Metadata = {
  title: "42 - AI Troubleshooting Agent",
  description: "Empower your troubleshooting with intelligent AI diagnostics",
    generator: 'v0.app'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${spaceGrotesk.variable} ${dmSans.variable} ${robotoMono.variable} antialiased`}>
      <body className="font-sans">{children}</body>
    </html>
  )
}
