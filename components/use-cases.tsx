"use client"

import useEmblaCarousel from "embla-carousel-react"
import { useCallback } from "react"

const useCases: Array<{ src: string; alt: string; description: string }> = [
  {
    src: "/use_case_1.jpeg",
    alt: "Use case 1",
    description: "TCP Handshake Failed, SSL Handshake Failed, VPN Tunnel Timeout, API Authentication Failed",
  },
  {
    src: "/use_case_2.jpeg",
    alt: "Use case 2",
    description: "DNS Timeout, HTTP Request Timeout, TCP Connection Timeout, API Call Timeout",
  },
  {
    src: "/use_case_3.jpeg",
    alt: "Use case 3",
    description: "Packet Loss, Out Of Order, One Way Traffic, Link Down, Incomplete Transfer",
  },
  {
    src: "/use_case_4.jpeg",
    alt: "Use case 4",
    description: "VoIP High Latency, Jitter, RTP Packet Loss, Choppy Audio, One-Way Audio",
  },
  {
    src: "/use_case_5.jpeg",
    alt: "Use case 5",
    description: "Routing Loop, Broadcast Storm, Infinite Redirects, DNS Loop",
  },
  {
    src: "/use_case_6.jpeg",
    alt: "Use case 6",
    description: "DDoS Attack, Traffic Flooding, Traffic Spike, Download Surge",
  },
]

export default function UseCases() {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, align: "start" })

  const scrollPrev = useCallback(() => emblaApi?.scrollPrev(), [emblaApi])
  const scrollNext = useCallback(() => emblaApi?.scrollNext(), [emblaApi])

  return (
    <section id="use-cases" className="bg-white py-20">
      <div className="container mx-auto px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-10">
            <h2 className="font-space-grotesk font-bold text-5xl text-cyan-800 mb-4 tracking-tight">Use Cases</h2>
            <p className="font-space-grotesk text-xl text-slate-600 font-light">
              When the network stops, so does everything else.
            </p>
          </div>

          <div className="relative">
            {/* Desktop overlay controls - Added frosted glass effect to circular buttons */}
            <button
              aria-label="Previous use case"
              onClick={scrollPrev}
              className="hidden md:flex absolute left-4 top-1/2 -translate-y-1/2 z-20 bg-white/20 backdrop-blur-md hover:bg-white/30 text-cyan-800 rounded-full w-12 h-12 items-center justify-center shadow-lg border border-white/30 cursor-pointer active:scale-95 transition-all"
            >
              <span className="text-2xl leading-none">‹</span>
            </button>
            <button
              aria-label="Next use case"
              onClick={scrollNext}
              className="hidden md:flex absolute right-4 top-1/2 -translate-y-1/2 z-20 bg-white/20 backdrop-blur-md hover:bg-white/30 text-cyan-800 rounded-full w-12 h-12 items-center justify-center shadow-lg border border-white/30 cursor-pointer active:scale-95 transition-all"
            >
              <span className="text-2xl leading-none">›</span>
            </button>

            <div className="overflow-hidden shadow-xl">
              <div className="embla__viewport overflow-hidden" ref={emblaRef}>
                <div className="embla__container flex">
                  {useCases.map((uc, idx) => (
                    <div key={idx} className="embla__slide relative flex-[0_0_100%] min-w-0">
                      <img
                        src={uc.src || "/placeholder.svg"}
                        alt={uc.alt}
                        className="w-full h-[320px] md:h-[520px] object-cover select-none"
                      />

                      {/* Caption Bar */}
                      <div className="absolute bottom-0 left-0 right-0">
                        <div className="bg-cyan-800/90 backdrop-blur-sm text-white px-4 md:px-8 py-3 md:py-5">
                          <p className="text-center font-space-grotesk font-semibold italic tracking-wide text-[12px] md:text-lg leading-snug">
                            {uc.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Mobile controls - Removed rounded corners from mobile buttons */}
            <div className="md:hidden flex items-center justify-between gap-3 mt-4">
              <button
                onClick={scrollPrev}
                className="flex-1 bg-cyan-700 text-white py-2 font-space-grotesk font-medium hover:bg-cyan-600 transition-all cursor-pointer active:scale-95"
              >
                Previous
              </button>
              <button
                onClick={scrollNext}
                className="flex-1 bg-emerald-600 text-white py-2 font-space-grotesk font-medium hover:bg-emerald-500 transition-all cursor-pointer active:scale-95"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
